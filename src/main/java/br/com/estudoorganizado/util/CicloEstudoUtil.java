package br.com.estudoorganizado.util;

import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Utilitário para geração de ciclos de estudo eficientes.
 * Implementa algoritmos para distribuição inteligente de tempo de estudo.
 */
public class CicloEstudoUtil {
    
    /**
     * Gera um ciclo de estudo otimizado aplicando diretamente às disciplinas.
     *
     * @param disciplinas Lista de disciplinas com peso e nível de conhecimento
     * @param horasSemanais Horas totais disponíveis por semana
     * @param duracaoMaximaSessao Duração máxima em minutos por sessão de estudo
     */
    public static void gerarCicloEstudo(List<CicloEstudoDisciplinaDTO> disciplinas,
                                        double horasSemanais,
                                        int duracaoMaximaSessao) {

        if (disciplinas == null || disciplinas.isEmpty()) {
            return;
        }

        // 1. Calcular prioridades e distribuir tempo
        Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina = calcularDistribuicaoTempo(
            disciplinas, horasSemanais);

        // 2. Aplicar tempos e ordem diretamente às disciplinas
        aplicarTemposEOrdem(temposPorDisciplina, duracaoMaximaSessao);
    }
    
    /**
     * Calcula a distribuição de tempo por disciplina baseada em peso e nível de conhecimento.
     */
    private static Map<CicloEstudoDisciplinaDTO, Integer> calcularDistribuicaoTempo(
            List<CicloEstudoDisciplinaDTO> disciplinas, double horasSemanais) {
        
        // Calcular prioridade total
        int totalPrioridade = disciplinas.stream()
            .mapToInt(d -> {
                int peso = d.getPeso() != null ? d.getPeso() : 1;
                int nivel = d.getNivelConhecimento() != null ? d.getNivelConhecimento() : 3;
                return peso * (6 - nivel); // Quanto menor o nível, maior a prioridade
            })
            .sum();
        
        Map<CicloEstudoDisciplinaDTO, Integer> distribuicao = new HashMap<>();
        int minutosSemanais = (int) (horasSemanais * 60);
        
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            int peso = disciplina.getPeso() != null ? disciplina.getPeso() : 1;
            int nivel = disciplina.getNivelConhecimento() != null ? disciplina.getNivelConhecimento() : 3;
            int prioridade = peso * (6 - nivel);
            
            int minutosDisciplina = totalPrioridade > 0 ? 
                (prioridade * minutosSemanais) / totalPrioridade : 
                minutosSemanais / disciplinas.size();
                
            distribuicao.put(disciplina, minutosDisciplina);
        }
        
        return distribuicao;
    }
    
    /**
     * Aplica os tempos calculados e ordem às disciplinas diretamente.
     */
    private static void aplicarTemposEOrdem(Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina,
                                           int duracaoMaximaSessao) {

        // Ordenar disciplinas por prioridade (maior prioridade = menor ordem)
        List<Map.Entry<CicloEstudoDisciplinaDTO, Integer>> disciplinasOrdenadas =
            temposPorDisciplina.entrySet().stream()
                .sorted((e1, e2) -> Integer.compare(e2.getValue(), e1.getValue())) // Decrescente por tempo
                .collect(Collectors.toList());

        int ordem = 1;
        for (Map.Entry<CicloEstudoDisciplinaDTO, Integer> entry : disciplinasOrdenadas) {
            CicloEstudoDisciplinaDTO disciplina = entry.getKey();
            int tempoTotal = entry.getValue();

            // Calcular tempo médio por sessão (respeitando duração máxima)
            int numSessoes = (int) Math.ceil((double) tempoTotal / duracaoMaximaSessao);
            int tempoMedioPorSessao = numSessoes > 0 ? tempoTotal / numSessoes : tempoTotal;

            // Aplicar valores à disciplina
            disciplina.setOrdem(ordem++);
            disciplina.setTempoEstudoMeta(br.com.estudoorganizado.util.Util.getLocalTimeHHmmss(
                String.format("%02d:%02d:00", tempoMedioPorSessao / 60, tempoMedioPorSessao % 60)));
        }
    }

}
