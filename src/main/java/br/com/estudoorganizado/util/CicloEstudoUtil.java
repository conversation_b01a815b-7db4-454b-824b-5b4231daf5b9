package br.com.estudoorganizado.util;

import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;
import br.com.estudoorganizado.dto.SessaoEstudoDTO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Utilitário para geração de ciclos de estudo eficientes.
 * Implementa algoritmos para distribuição inteligente de tempo de estudo.
 */
public class CicloEstudoUtil {
    
    /**
     * Gera um ciclo de estudo otimizado baseado nas disciplinas, horas disponíveis e duração máxima por sessão.
     * 
     * @param disciplinas Lista de disciplinas com peso e nível de conhecimento
     * @param horasSemanais Horas totais disponíveis por semana
     * @param duracaoMaximaSessao Duração máxima em minutos por sessão de estudo
     * @return Lista de sessões de estudo organizadas em ciclo alternado
     */
    public static List<SessaoEstudoDTO> gerarCicloEstudo(List<CicloEstudoDisciplinaDTO> disciplinas, 
                                                         double horasSemanais, 
                                                         int duracaoMaximaSessao) {
        
        if (disciplinas == null || disciplinas.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 1. Calcular prioridades e distribuir tempo
        Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina = calcularDistribuicaoTempo(
            disciplinas, horasSemanais);
        
        // 2. Criar sessões individuais para cada disciplina
        List<SessaoEstudoDTO> todasSessoes = criarSessoesIndividuais(
            temposPorDisciplina, duracaoMaximaSessao);
        
        // 3. Organizar sessões em ciclo alternado
        List<SessaoEstudoDTO> cicloOrganizado = organizarCicloAlternado(todasSessoes);
        
        // 4. Ajustar para usar exatamente as horas semanais disponíveis
        return ajustarParaHorasExatas(cicloOrganizado, horasSemanais);
    }
    
    /**
     * Calcula a distribuição de tempo por disciplina baseada em peso e nível de conhecimento.
     */
    private static Map<CicloEstudoDisciplinaDTO, Integer> calcularDistribuicaoTempo(
            List<CicloEstudoDisciplinaDTO> disciplinas, double horasSemanais) {
        
        // Calcular prioridade total
        int totalPrioridade = disciplinas.stream()
            .mapToInt(d -> {
                int peso = d.getPeso() != null ? d.getPeso() : 1;
                int nivel = d.getNivelConhecimento() != null ? d.getNivelConhecimento() : 3;
                return peso * (6 - nivel); // Quanto menor o nível, maior a prioridade
            })
            .sum();
        
        Map<CicloEstudoDisciplinaDTO, Integer> distribuicao = new HashMap<>();
        int minutosSemanais = (int) (horasSemanais * 60);
        
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            int peso = disciplina.getPeso() != null ? disciplina.getPeso() : 1;
            int nivel = disciplina.getNivelConhecimento() != null ? disciplina.getNivelConhecimento() : 3;
            int prioridade = peso * (6 - nivel);
            
            int minutosDisciplina = totalPrioridade > 0 ? 
                (prioridade * minutosSemanais) / totalPrioridade : 
                minutosSemanais / disciplinas.size();
                
            distribuicao.put(disciplina, minutosDisciplina);
        }
        
        return distribuicao;
    }
    
    /**
     * Cria sessões individuais para cada disciplina respeitando a duração máxima.
     */
    private static List<SessaoEstudoDTO> criarSessoesIndividuais(
            Map<CicloEstudoDisciplinaDTO, Integer> temposPorDisciplina, 
            int duracaoMaximaSessao) {
        
        List<SessaoEstudoDTO> sessoes = new ArrayList<>();
        
        for (Map.Entry<CicloEstudoDisciplinaDTO, Integer> entry : temposPorDisciplina.entrySet()) {
            CicloEstudoDisciplinaDTO disciplina = entry.getKey();
            int tempoTotal = entry.getValue();
            
            // Dividir o tempo total em sessões menores
            while (tempoTotal > 0) {
                int duracaoSessao = Math.min(tempoTotal, duracaoMaximaSessao);
                sessoes.add(new SessaoEstudoDTO(0, duracaoSessao, disciplina));
                tempoTotal -= duracaoSessao;
            }
        }
        
        return sessoes;
    }
    
    /**
     * Organiza as sessões em um ciclo alternado para evitar estudar a mesma disciplina consecutivamente.
     */
    private static List<SessaoEstudoDTO> organizarCicloAlternado(List<SessaoEstudoDTO> sessoes) {
        // Agrupar sessões por disciplina
        Map<String, List<SessaoEstudoDTO>> sessoesPorDisciplina = sessoes.stream()
            .collect(Collectors.groupingBy(s -> {
                if (s.getDisciplina() != null && s.getDisciplina().getDisciplina() != null) {
                    return s.getDisciplina().getDisciplina().getNome();
                }
                return "Disciplina Desconhecida";
            }));
        
        List<SessaoEstudoDTO> cicloOrganizado = new ArrayList<>();
        
        // Alternar entre disciplinas até esgotar todas as sessões
        while (!sessoesPorDisciplina.isEmpty()) {
            Iterator<Map.Entry<String, List<SessaoEstudoDTO>>> iterator = 
                sessoesPorDisciplina.entrySet().iterator();
            
            while (iterator.hasNext()) {
                Map.Entry<String, List<SessaoEstudoDTO>> entry = iterator.next();
                List<SessaoEstudoDTO> sessoesDisciplina = entry.getValue();
                
                if (!sessoesDisciplina.isEmpty()) {
                    SessaoEstudoDTO sessao = sessoesDisciplina.remove(0);
                    sessao.setOrdem(cicloOrganizado.size() + 1);
                    cicloOrganizado.add(sessao);
                }
                
                if (sessoesDisciplina.isEmpty()) {
                    iterator.remove();
                }
            }
        }
        
        return cicloOrganizado;
    }
    
    /**
     * Ajusta o ciclo para usar exatamente as horas semanais disponíveis.
     */
    private static List<SessaoEstudoDTO> ajustarParaHorasExatas(
            List<SessaoEstudoDTO> ciclo, double horasSemanais) {
        
        int minutosDesejados = (int) (horasSemanais * 60);
        int minutosAtuais = ciclo.stream()
            .mapToInt(SessaoEstudoDTO::getDuracaoMinutos)
            .sum();
        
        int diferenca = minutosDesejados - minutosAtuais;
        
        // Se há diferença, ajustar proporcionalmente
        if (diferenca != 0 && !ciclo.isEmpty()) {
            // Distribuir a diferença proporcionalmente entre as sessões
            for (SessaoEstudoDTO sessao : ciclo) {
                double proporcao = (double) sessao.getDuracaoMinutos() / minutosAtuais;
                int ajuste = (int) Math.round(diferenca * proporcao);
                int novaDuracao = Math.max(15, sessao.getDuracaoMinutos() + ajuste); // Mínimo 15 min
                sessao.setDuracaoMinutos(novaDuracao);
            }
        }
        
        return ciclo;
    }
}
