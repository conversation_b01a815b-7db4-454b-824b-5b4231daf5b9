package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.model.CicloEstudo;
import br.com.estudoorganizado.model.Planejamento;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.PlanejamentoRepository;
import br.com.estudoorganizado.util.Util;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class PlanejamentoService {

    private final PlanejamentoRepository planejamentoRepository;
    private final CicloEstudoService cicloEstudoService;
    private final ModelMapper modelMapper;

    public PlanejamentoDTO saveOrUpdate(PlanejamentoDTO dto, User user) {
        validarDados(dto);

        Planejamento planejamentoEntity = planejamentoRepository.findByUserId(user.getId())
                .orElse(null);

        if (dto.getCicloEstudo() != null) {
            CicloEstudoDTO cicloEstudoDTO;
            if (Util.emptyNumber(dto.getCicloEstudo().getId())) {
                cicloEstudoDTO = cicloEstudoService.save(dto.getCicloEstudo(), user);
            } else {
                cicloEstudoDTO = cicloEstudoService.update(dto.getCicloEstudo().getId(), user, dto.getCicloEstudo());
            }
            dto.setCicloEstudo(cicloEstudoDTO);
        }

        planejamentoEntity = planejamentoEntity == null ? new Planejamento() : planejamentoEntity;
        planejamentoEntity.setNome(dto.getNome());
        planejamentoEntity.setUser(user);
        planejamentoEntity.setDataCriacao(dto.getDataCriacao() != null ? dto.getDataCriacao() : LocalDateTime.now());
        if (dto.getCicloEstudo() != null && !Util.emptyNumber(dto.getCicloEstudo().getId())) {
            CicloEstudo cicloEstudo = cicloEstudoService.findById(dto.getCicloEstudo().getId());
            if (cicloEstudo != null) {
                planejamentoEntity.setCicloEstudo(cicloEstudo);
            }
        }
        planejamentoEntity.setHorasDisponiveisPorSemana(dto.getHorasDisponiveisPorSemana());
        planejamentoEntity.setMinutosDuracaoMaximaPorSessao(dto.getMinutosDuracaoMaximaPorSessao());
        planejamentoEntity.setIntervalosRevisao(dto.getIntervalosRevisao());

        Planejamento savedEntity = planejamentoRepository.save(planejamentoEntity);
        return modelMapper.map(savedEntity, PlanejamentoDTO.class);
    }

    public PlanejamentoDTO findByUserId(Long userId) {
        Planejamento planejamento = planejamentoRepository.findByUserId(userId)
                .orElse(null);
        if (planejamento == null) {
            return null;
        }
        return modelMapper.map(planejamento, PlanejamentoDTO.class);
    }

    private void validarDados(PlanejamentoDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("Planejamento não informado!");
        }
        if (Util.emptyString(dto.getNome())) {
            throw new IllegalArgumentException("Nome da planejamento não foi informado!");
        }
        if (Util.emptyNumber(dto.getHorasDisponiveisPorSemana())) {
            throw new IllegalArgumentException("Horas disponíveis por semana para estudar não foram informados!");
        }
        if (Util.emptyNumber(dto.getMinutosDuracaoMaximaPorSessao())) {
            throw new IllegalArgumentException("Duração máxima por sessão de estudo não foi informada!");
        }
        if (Util.emptyString(dto.getIntervalosRevisao())) {
            throw new IllegalArgumentException("Intervalos revisão espaçada não foram informados");
        }
    }

    /**
     * Gera sugestão automática de ciclo de estudo baseado nas regras de planejamento.
     */
    public PlanejamentoDTO sugerirPlanejamentoCicloEstudo(PlanejamentoDTO planejamentoDTO) {
        if (planejamentoDTO == null || planejamentoDTO.getCicloEstudo().getDisciplinas() == null || planejamentoDTO.getCicloEstudo().getDisciplinas().isEmpty()) {
            throw new IllegalArgumentException("Disciplinas não informadas para sugestão de ciclo de estudo.");
        }
        int totalPrioridade = 0;
        for (var d : planejamentoDTO.getCicloEstudo().getDisciplinas()) {
            int peso = d.getPeso() != null ? d.getPeso() : 1;
            int nivel = d.getNivelConhecimento() != null ? d.getNivelConhecimento() : 3;
            totalPrioridade += peso * (6 - nivel);
        }
        double horasSemana = planejamentoDTO.getHorasDisponiveisPorSemana() != null ? planejamentoDTO.getHorasDisponiveisPorSemana() : 10;
        int duracaoMaximaSessao = planejamentoDTO.getMinutosDuracaoMaximaPorSessao() != null ? planejamentoDTO.getMinutosDuracaoMaximaPorSessao() : 90;
        int ordem = 1;
        for (var d : planejamentoDTO.getCicloEstudo().getDisciplinas()) {
            int peso = d.getPeso() != null ? d.getPeso() : 1;
            int nivel = d.getNivelConhecimento() != null ? d.getNivelConhecimento() : 3;
            int prioridade = peso * (6 - nivel);
            double horasDisciplina = totalPrioridade > 0 ? (prioridade * horasSemana) / (double) totalPrioridade : 0;
            int minutosDisciplina = (int) Math.round(horasDisciplina * 60);
            int numSessoes = (int) Math.ceil((double) minutosDisciplina / duracaoMaximaSessao);
            int tempoPorSessao = numSessoes > 0 ? (int) Math.ceil((double) minutosDisciplina / numSessoes) : 0;
            d.setTempoEstudoMeta(Util.getLocalTimeHHmmss(String.format("%02d:%02d:00", tempoPorSessao / 60, tempoPorSessao % 60)));
            d.setOrdem(ordem++);
        }
        return planejamentoDTO;
    }
}
