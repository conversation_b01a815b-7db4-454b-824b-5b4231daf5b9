package br.com.estudoorganizado.dto;

import lombok.Data;

/**
 * DTO para representar uma sessão de estudo individual dentro de um ciclo.
 * Cada sessão representa um período de estudo de uma disciplina específica.
 */
@Data
public class SessaoEstudoDTO {
    
    private Integer ordem;
    private Integer duracaoMinutos;
    private CicloEstudoDisciplinaDTO disciplina;
    
    public SessaoEstudoDTO() {}
    
    public SessaoEstudoDTO(Integer ordem, Integer duracaoMinutos, CicloEstudoDisciplinaDTO disciplina) {
        this.ordem = ordem;
        this.duracaoMinutos = duracaoMinutos;
        this.disciplina = disciplina;
    }
}
