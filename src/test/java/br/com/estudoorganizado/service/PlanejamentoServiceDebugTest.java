package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.repository.PlanejamentoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.time.LocalTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PlanejamentoServiceDebugTest {

    @Mock
    private PlanejamentoRepository planejamentoRepository;

    @Mock
    private CicloEstudoService cicloEstudoService;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private PlanejamentoService planejamentoService;

    @Test
    void testSugerirPlanejamentoCicloEstudo_DebugSessoes() {
        // Arrange
        PlanejamentoDTO planejamento = criarPlanejamentoExemplo();

        System.out.println("=== ANTES DA SUGESTÃO ===");
        imprimirDisciplinas(planejamento.getCicloEstudo().getDisciplinas());

        // Act
        PlanejamentoDTO resultado = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamento);

        // Assert & Debug
        System.out.println("\n=== DEPOIS DA SUGESTÃO ===");
        System.out.println("Número de sessões geradas: " + resultado.getCicloEstudo().getDisciplinas().size());
        imprimirDisciplinas(resultado.getCicloEstudo().getDisciplinas());

        // Calcular tempo total
        int tempoTotalMinutos = 0;
        for (CicloEstudoDisciplinaDTO disciplina : resultado.getCicloEstudo().getDisciplinas()) {
            LocalTime tempo = disciplina.getTempoEstudoMeta();
            int minutos = tempo.getHour() * 60 + tempo.getMinute();
            tempoTotalMinutos += minutos;
        }

        System.out.println("\n=== RESUMO ===");
        System.out.println("Tempo total do ciclo completo: " + tempoTotalMinutos + " minutos");
        System.out.println("Horas semanais disponíveis: " + planejamento.getHorasDisponiveisPorSemana() + "h (" + (planejamento.getHorasDisponiveisPorSemana() * 60) + " minutos)");
        System.out.println("Aproveitamento: " + String.format("%.1f%%", (tempoTotalMinutos * 100.0) / (planejamento.getHorasDisponiveisPorSemana() * 60)));

        assertNotNull(resultado);
        assertNotNull(resultado.getCicloEstudo());
        assertNotNull(resultado.getCicloEstudo().getDisciplinas());

        // Verificar se foram geradas mais sessões que as disciplinas originais
        assertTrue(resultado.getCicloEstudo().getDisciplinas().size() > 3,
                "Devem ser geradas mais sessões que as 3 disciplinas originais");

        // Verificar se todas as sessões têm tempo e ordem definidos
        for (CicloEstudoDisciplinaDTO sessao : resultado.getCicloEstudo().getDisciplinas()) {
            assertNotNull(sessao.getTempoEstudoMeta(),
                    "Sessão " + sessao.getDisciplina().getNome() + " deve ter tempo de estudo meta");
            assertNotNull(sessao.getOrdem(),
                    "Sessão " + sessao.getDisciplina().getNome() + " deve ter ordem definida");

            // Verificar se o tempo não é zero
            LocalTime tempo = sessao.getTempoEstudoMeta();
            int minutos = tempo.getHour() * 60 + tempo.getMinute();
            assertTrue(minutos > 0,
                    "Sessão " + sessao.getDisciplina().getNome() + " deve ter tempo > 0");

            // Verificar se não excede duração máxima
            assertTrue(minutos <= 90,
                    "Sessão não deve exceder 90 minutos");
        }

        // Verificar se há alternância entre disciplinas
        Set<String> disciplinasPresentes = resultado.getCicloEstudo().getDisciplinas().stream()
                .map(d -> d.getDisciplina().getNome())
                .collect(java.util.stream.Collectors.toSet());
        assertEquals(3, disciplinasPresentes.size(), "Todas as 3 disciplinas devem estar presentes no ciclo");

        // Verificar se Português tem mais sessões que Matemática (devido à maior prioridade)
        long sessoesPortugues = resultado.getCicloEstudo().getDisciplinas().stream()
                .filter(d -> "Português".equals(d.getDisciplina().getNome()))
                .count();
        long sessoesMatematica = resultado.getCicloEstudo().getDisciplinas().stream()
                .filter(d -> "Matemática".equals(d.getDisciplina().getNome()))
                .count();

        assertTrue(sessoesPortugues >= sessoesMatematica,
                "Português deve ter pelo menos tantas sessões quanto Matemática devido ao maior peso e menor nível");
    }

    private void imprimirDisciplinas(java.util.List<CicloEstudoDisciplinaDTO> disciplinas) {
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            System.out.printf("Disciplina: %s | Peso: %d | Nível: %d | Ordem: %s | Tempo: %s%n",
                    disciplina.getDisciplina().getNome(),
                    disciplina.getPeso(),
                    disciplina.getNivelConhecimento(),
                    disciplina.getOrdem(),
                    disciplina.getTempoEstudoMeta());
        }
    }

    private PlanejamentoDTO criarPlanejamentoExemplo() {
        PlanejamentoDTO dto = new PlanejamentoDTO();
        dto.setNome("Planejamento Teste");
        dto.setHorasDisponiveisPorSemana(10);
        dto.setMinutosDuracaoMaximaPorSessao(90);

        CicloEstudoDTO cicloEstudo = new CicloEstudoDTO();
        cicloEstudo.setNome("Ciclo Teste");
        cicloEstudo.setDisciplinas(Arrays.asList(
                criarDisciplina("Português", 5, 2),
                criarDisciplina("Matemática", 3, 4),
                criarDisciplina("Direito Administrativo", 4, 3)
        ));

        dto.setCicloEstudo(cicloEstudo);
        return dto;
    }

    private CicloEstudoDisciplinaDTO criarDisciplina(String nome, int peso, int nivelConhecimento) {
        DisciplinaDTO disciplinaDTO = new DisciplinaDTO();
        disciplinaDTO.setNome(nome);

        CicloEstudoDisciplinaDTO cicloEstudoDisciplinaDTO = new CicloEstudoDisciplinaDTO();
        cicloEstudoDisciplinaDTO.setDisciplina(disciplinaDTO);
        cicloEstudoDisciplinaDTO.setPeso(peso);
        cicloEstudoDisciplinaDTO.setNivelConhecimento(nivelConhecimento);

        return cicloEstudoDisciplinaDTO;
    }
}
