package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.repository.PlanejamentoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.time.LocalTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PlanejamentoServiceDebugTest {

    @Mock
    private PlanejamentoRepository planejamentoRepository;

    @Mock
    private CicloEstudoService cicloEstudoService;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private PlanejamentoService planejamentoService;

    @Test
    void testSugerirPlanejamentoCicloEstudo_DebugSessoes() {
        // Arrange
        PlanejamentoDTO planejamento = criarPlanejamentoExemplo();

        System.out.println("=== ANTES DA SUGESTÃO ===");
        imprimirDisciplinas(planejamento.getCicloEstudo().getDisciplinas());

        // Act
        PlanejamentoDTO resultado = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamento);

        // Assert & Debug
        System.out.println("\n=== DEPOIS DA SUGESTÃO ===");
        imprimirDisciplinas(resultado.getCicloEstudo().getDisciplinas());

        // Calcular tempo total
        int tempoTotalMinutos = 0;
        for (CicloEstudoDisciplinaDTO disciplina : resultado.getCicloEstudo().getDisciplinas()) {
            LocalTime tempo = disciplina.getTempoEstudoMeta();
            int minutos = tempo.getHour() * 60 + tempo.getMinute();
            tempoTotalMinutos += minutos;
        }

        System.out.println("\n=== RESUMO ===");
        System.out.println("Tempo total por ciclo: " + tempoTotalMinutos + " minutos");
        System.out.println("Horas semanais disponíveis: " + planejamento.getHorasDisponiveisPorSemana() + "h (" + (planejamento.getHorasDisponiveisPorSemana() * 60) + " minutos)");
        System.out.println("Ciclos possíveis por semana: " + String.format("%.1f", (planejamento.getHorasDisponiveisPorSemana() * 60.0) / tempoTotalMinutos));

        assertNotNull(resultado);
        assertNotNull(resultado.getCicloEstudo());
        assertNotNull(resultado.getCicloEstudo().getDisciplinas());

        // Verificar se todas as disciplinas têm tempo e ordem definidos
        for (CicloEstudoDisciplinaDTO disciplina : resultado.getCicloEstudo().getDisciplinas()) {
            assertNotNull(disciplina.getTempoEstudoMeta(),
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter tempo de estudo meta");
            assertNotNull(disciplina.getOrdem(),
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter ordem definida");

            // Verificar se o tempo não é zero
            LocalTime tempo = disciplina.getTempoEstudoMeta();
            int minutos = tempo.getHour() * 60 + tempo.getMinute();
            assertTrue(minutos > 0,
                    "Disciplina " + disciplina.getDisciplina().getNome() + " deve ter tempo > 0");
        }

        // Verificar distribuição proporcional
        CicloEstudoDisciplinaDTO portugues = resultado.getCicloEstudo().getDisciplinas().stream()
                .filter(d -> "Português".equals(d.getDisciplina().getNome()))
                .findFirst().orElse(null);
        CicloEstudoDisciplinaDTO matematica = resultado.getCicloEstudo().getDisciplinas().stream()
                .filter(d -> "Matemática".equals(d.getDisciplina().getNome()))
                .findFirst().orElse(null);

        assertNotNull(portugues);
        assertNotNull(matematica);

        int minutosPortugues = portugues.getTempoEstudoMeta().getHour() * 60 + portugues.getTempoEstudoMeta().getMinute();
        int minutosMatematica = matematica.getTempoEstudoMeta().getHour() * 60 + matematica.getTempoEstudoMeta().getMinute();

        assertTrue(minutosPortugues > minutosMatematica,
                "Português deve ter mais tempo que Matemática devido ao maior peso e menor nível");
    }

    private void imprimirDisciplinas(java.util.List<CicloEstudoDisciplinaDTO> disciplinas) {
        for (CicloEstudoDisciplinaDTO disciplina : disciplinas) {
            System.out.printf("Disciplina: %s | Peso: %d | Nível: %d | Ordem: %s | Tempo: %s%n",
                    disciplina.getDisciplina().getNome(),
                    disciplina.getPeso(),
                    disciplina.getNivelConhecimento(),
                    disciplina.getOrdem(),
                    disciplina.getTempoEstudoMeta());
        }
    }

    private PlanejamentoDTO criarPlanejamentoExemplo() {
        PlanejamentoDTO dto = new PlanejamentoDTO();
        dto.setNome("Planejamento Teste");
        dto.setHorasDisponiveisPorSemana(10);
        dto.setMinutosDuracaoMaximaPorSessao(90);

        CicloEstudoDTO cicloEstudo = new CicloEstudoDTO();
        cicloEstudo.setNome("Ciclo Teste");
        cicloEstudo.setDisciplinas(Arrays.asList(
                criarDisciplina("Português", 5, 2),
                criarDisciplina("Matemática", 3, 4),
                criarDisciplina("Direito Administrativo", 4, 3)
        ));

        dto.setCicloEstudo(cicloEstudo);
        return dto;
    }

    private CicloEstudoDisciplinaDTO criarDisciplina(String nome, int peso, int nivelConhecimento) {
        DisciplinaDTO disciplinaDTO = new DisciplinaDTO();
        disciplinaDTO.setNome(nome);

        CicloEstudoDisciplinaDTO cicloEstudoDisciplinaDTO = new CicloEstudoDisciplinaDTO();
        cicloEstudoDisciplinaDTO.setDisciplina(disciplinaDTO);
        cicloEstudoDisciplinaDTO.setPeso(peso);
        cicloEstudoDisciplinaDTO.setNivelConhecimento(nivelConhecimento);

        return cicloEstudoDisciplinaDTO;
    }
}
