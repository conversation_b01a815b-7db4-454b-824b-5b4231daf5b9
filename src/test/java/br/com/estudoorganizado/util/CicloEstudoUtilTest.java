package br.com.estudoorganizado.util;

import br.com.estudoorganizado.dto.CicloEstudoDisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.SessaoEstudoDTO;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

class CicloEstudoUtilTest {

    @Test
    void testGerarCicloEstudo_ComTresDisciplinas() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = criarDisciplinasExemplo();
        double horasSemanais = 10.0; // 600 minutos
        int duracaoMaximaSessao = 90; // 90 minutos

        // Act
        List<SessaoEstudoDTO> ciclo = CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        assertNotNull(ciclo);
        assertFalse(ciclo.isEmpty());
        
        // Verificar se o tempo total está próximo das horas semanais (tolerância de ±5%)
        int tempoTotalMinutos = ciclo.stream().mapToInt(SessaoEstudoDTO::getDuracaoMinutos).sum();
        int tempoEsperado = (int) (horasSemanais * 60);
        assertTrue(Math.abs(tempoTotalMinutos - tempoEsperado) <= tempoEsperado * 0.05, 
                   "Tempo total deve estar próximo de " + tempoEsperado + " minutos, mas foi " + tempoTotalMinutos);
        
        // Verificar se nenhuma sessão excede a duração máxima
        assertTrue(ciclo.stream().allMatch(s -> s.getDuracaoMinutos() <= duracaoMaximaSessao),
                   "Nenhuma sessão deve exceder " + duracaoMaximaSessao + " minutos");
        
        // Verificar se todas as disciplinas estão presentes
        List<String> disciplinasNoCiclo = ciclo.stream()
                .map(s -> s.getDisciplina().getDisciplina().getNome())
                .distinct()
                .collect(Collectors.toList());
        assertEquals(3, disciplinasNoCiclo.size(), "Todas as 3 disciplinas devem estar presentes no ciclo");
    }

    @Test
    void testGerarCicloEstudo_DistribuicaoPorPesoENivel() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = criarDisciplinasExemplo();
        double horasSemanais = 10.0;
        int duracaoMaximaSessao = 90;

        // Act
        List<SessaoEstudoDTO> ciclo = CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        Map<String, Integer> tempoPorDisciplina = ciclo.stream()
                .collect(Collectors.groupingBy(
                        s -> s.getDisciplina().getDisciplina().getNome(),
                        Collectors.summingInt(SessaoEstudoDTO::getDuracaoMinutos)
                ));

        // Português (peso 5, nível 2) deve ter mais tempo que Matemática (peso 3, nível 4)
        int tempoPortugues = tempoPorDisciplina.get("Português");
        int tempoMatematica = tempoPorDisciplina.get("Matemática");
        assertTrue(tempoPortugues > tempoMatematica, 
                   "Português deve ter mais tempo que Matemática devido ao maior peso e menor nível");
    }

    @Test
    void testGerarCicloEstudo_AlternanciaEntreDisciplinas() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = criarDisciplinasExemplo();
        double horasSemanais = 6.0; // Menos horas para facilitar o teste
        int duracaoMaximaSessao = 90;

        // Act
        List<SessaoEstudoDTO> ciclo = CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        // Verificar que não há duas sessões consecutivas da mesma disciplina (quando possível)
        for (int i = 1; i < ciclo.size(); i++) {
            String disciplinaAtual = ciclo.get(i).getDisciplina().getDisciplina().getNome();
            String disciplinaAnterior = ciclo.get(i - 1).getDisciplina().getDisciplina().getNome();
            
            // Se há mais de uma disciplina no ciclo, não deveria haver repetição consecutiva
            if (ciclo.stream().map(s -> s.getDisciplina().getDisciplina().getNome()).distinct().count() > 1) {
                // Permitir repetição apenas se for a última sessão de uma disciplina
                boolean isUltimaSessaoDisciplina = ciclo.subList(i + 1, ciclo.size()).stream()
                        .noneMatch(s -> s.getDisciplina().getDisciplina().getNome().equals(disciplinaAtual));
                
                if (!isUltimaSessaoDisciplina) {
                    assertNotEquals(disciplinaAtual, disciplinaAnterior,
                            "Não deveria haver sessões consecutivas da mesma disciplina na posição " + i);
                }
            }
        }
    }

    @Test
    void testGerarCicloEstudo_ComListaVazia() {
        // Arrange
        List<CicloEstudoDisciplinaDTO> disciplinas = Arrays.asList();
        double horasSemanais = 10.0;
        int duracaoMaximaSessao = 90;

        // Act
        List<SessaoEstudoDTO> ciclo = CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        assertNotNull(ciclo);
        assertTrue(ciclo.isEmpty());
    }

    @Test
    void testGerarCicloEstudo_ComUmaDisciplina() {
        // Arrange
        CicloEstudoDisciplinaDTO disciplina = criarDisciplina("Direito Constitucional", 4, 3);
        List<CicloEstudoDisciplinaDTO> disciplinas = Arrays.asList(disciplina);
        double horasSemanais = 5.0; // 300 minutos
        int duracaoMaximaSessao = 90;

        // Act
        List<SessaoEstudoDTO> ciclo = CicloEstudoUtil.gerarCicloEstudo(disciplinas, horasSemanais, duracaoMaximaSessao);

        // Assert
        assertNotNull(ciclo);
        assertFalse(ciclo.isEmpty());
        
        // Todas as sessões devem ser da mesma disciplina
        assertTrue(ciclo.stream().allMatch(s -> 
                s.getDisciplina().getDisciplina().getNome().equals("Direito Constitucional")));
        
        // Tempo total deve estar próximo das horas semanais
        int tempoTotal = ciclo.stream().mapToInt(SessaoEstudoDTO::getDuracaoMinutos).sum();
        int tempoEsperado = 300;
        assertTrue(Math.abs(tempoTotal - tempoEsperado) <= 30, 
                   "Tempo total deve estar próximo de " + tempoEsperado + " minutos");
    }

    private List<CicloEstudoDisciplinaDTO> criarDisciplinasExemplo() {
        return Arrays.asList(
                criarDisciplina("Português", 5, 2),      // Alta prioridade (peso alto, nível baixo)
                criarDisciplina("Matemática", 3, 4),     // Baixa prioridade (peso médio, nível alto)
                criarDisciplina("Direito Administrativo", 4, 3) // Prioridade média
        );
    }

    private CicloEstudoDisciplinaDTO criarDisciplina(String nome, int peso, int nivelConhecimento) {
        DisciplinaDTO disciplinaDTO = new DisciplinaDTO();
        disciplinaDTO.setNome(nome);

        CicloEstudoDisciplinaDTO cicloEstudoDisciplinaDTO = new CicloEstudoDisciplinaDTO();
        cicloEstudoDisciplinaDTO.setDisciplina(disciplinaDTO);
        cicloEstudoDisciplinaDTO.setPeso(peso);
        cicloEstudoDisciplinaDTO.setNivelConhecimento(nivelConhecimento);

        return cicloEstudoDisciplinaDTO;
    }
}
