# Melhorias na Geração de Ciclos de Estudo

## Visão Geral

Este documento descreve as melhorias implementadas no método `sugerirPlanejamentoCicloEstudo` do `PlanejamentoService`, que agora gera ciclos de estudo mais eficientes e pedagogicamente corretos.

## Problemas Identificados na Versão Anterior

### 1. Distribuição Inadequada de Tempo
- **Problema**: O método calculava tempo por disciplina individualmente, mas não criava um ciclo real de alternância
- **Impacto**: Usuário com 10h semanais e 3 disciplinas recebia apenas 3 "blocos" de estudo, não aproveitando todo o tempo disponível

### 2. Falta de Alternância Entre Disciplinas
- **Problema**: Não havia lógica para alternar entre disciplinas ao longo da semana
- **Impacto**: Estudante poderia ficar muito tempo na mesma disciplina, causando fadiga mental

### 3. Não Aproveitamento Total das Horas
- **Problema**: O tempo total das disciplinas não necessariamente somava as horas semanais disponíveis
- **Impacto**: Desperdício de tempo de estudo disponível

## Solução Implementada

### Nova Arquitetura

#### 1. Classe `SessaoEstudoDTO`
```java
public class SessaoEstudoDTO {
    private Integer ordem;
    private Integer duracaoMinutos;
    private CicloEstudoDisciplinaDTO disciplina;
}
```
Representa uma sessão individual de estudo dentro do ciclo.

#### 2. Classe `CicloEstudoUtil`
Utilitário responsável pela lógica de geração de ciclos otimizados.

### Algoritmo de Geração de Ciclo

#### Passo 1: Cálculo de Prioridades
```java
Prioridade = Peso × (6 - NívelConhecimento)
```
- **Peso**: Importância da disciplina (1-5)
- **Nível de Conhecimento**: 1 (iniciante) a 5 (avançado)
- Disciplinas com maior peso e menor conhecimento recebem mais tempo

#### Passo 2: Distribuição Proporcional
```java
TempoDisc = (PrioridadeDisc / TotalPrioridades) × HorasSemanais
```

#### Passo 3: Criação de Sessões
- Divide o tempo de cada disciplina em sessões menores
- Respeita a duração máxima por sessão (ex: 90 minutos)
- Cria múltiplas sessões por disciplina quando necessário

#### Passo 4: Organização em Ciclo Alternado
- Alterna entre disciplinas para evitar fadiga mental
- Distribui as sessões de forma equilibrada
- Garante que não há duas sessões consecutivas da mesma disciplina (quando possível)

#### Passo 5: Ajuste para Horas Exatas
- Ajusta proporcionalmente para usar exatamente as horas semanais disponíveis
- Garante que nenhuma sessão seja menor que 15 minutos

## Exemplo Prático

### Entrada
- **Horas semanais**: 10h (600 minutos)
- **Duração máxima por sessão**: 90 minutos
- **Disciplinas**:
  - Português: peso 5, nível 2 → prioridade = 5 × (6-2) = 20
  - Matemática: peso 3, nível 4 → prioridade = 3 × (6-4) = 6
  - Direito Administrativo: peso 4, nível 3 → prioridade = 4 × (6-3) = 12
- **Total de prioridades**: 38

### Distribuição de Tempo
- Português: (20/38) × 600 = 316 minutos
- Matemática: (6/38) × 600 = 95 minutos
- Direito Administrativo: (12/38) × 600 = 189 minutos

### Sessões Geradas
- Português: 4 sessões (90, 90, 90, 46 minutos)
- Matemática: 2 sessões (90, 5 minutos → ajustado para 47, 48 minutos)
- Direito Administrativo: 3 sessões (90, 90, 9 minutos → ajustado para 63, 63, 63 minutos)

### Ciclo Final (Exemplo)
1. Português - 90 min
2. Direito Administrativo - 63 min
3. Matemática - 47 min
4. Português - 90 min
5. Direito Administrativo - 63 min
6. Matemática - 48 min
7. Português - 90 min
8. Direito Administrativo - 63 min
9. Português - 46 min

**Total**: 600 minutos (10 horas exatas)

## Benefícios da Nova Implementação

### 1. Uso Completo do Tempo Disponível
- Garante que todas as horas semanais sejam utilizadas
- Elimina desperdício de tempo de estudo

### 2. Alternância Inteligente
- Evita fadiga mental por estudar muito tempo a mesma disciplina
- Mantém o foco e a motivação do estudante

### 3. Distribuição Proporcional Correta
- Disciplinas mais importantes e com menor domínio recebem mais tempo
- Respeita as prioridades definidas pelo usuário

### 4. Flexibilidade
- Funciona com qualquer número de disciplinas
- Adapta-se a diferentes cargas horárias e durações máximas de sessão

### 5. Pedagogicamente Correto
- Baseado em melhores práticas de ciclos de estudo para concursos
- Promove aprendizado mais eficiente

## Compatibilidade

A nova implementação mantém total compatibilidade com:
- DTOs existentes (`PlanejamentoDTO`, `CicloEstudoDTO`, `CicloEstudoDisciplinaDTO`)
- Interface do método `sugerirPlanejamentoCicloEstudo`
- Estrutura de dados retornada

## Testes Implementados

### Testes Unitários para `CicloEstudoUtil`
- Distribuição correta de tempo
- Alternância entre disciplinas
- Uso total das horas disponíveis
- Casos extremos (lista vazia, uma disciplina)

### Testes de Integração para `PlanejamentoService`
- Funcionamento com diferentes parâmetros
- Validação de entrada
- Compatibilidade com valores padrão

## Conclusão

As melhorias implementadas transformam o método de sugestão de ciclo de estudo de um simples calculador de tempo por disciplina em um gerador inteligente de ciclos otimizados, que:

1. **Aproveita todo o tempo disponível** do estudante
2. **Alterna entre disciplinas** para manter o foco
3. **Distribui o tempo proporcionalmente** baseado em importância e conhecimento
4. **Segue melhores práticas pedagógicas** para estudos de concurso

O resultado é um ciclo de estudo mais eficiente, que maximiza o aproveitamento do tempo e melhora a experiência de aprendizado do usuário.
